import Foundation

class StatisticsManager {
    private let userDefaults = UserDefaults.standard
    private let completedIntervalsKey = "completedIntervals"
    private let completedBreaksKey = "completedBreaks"

    init() {
        // Инициализация может включать миграцию данных если нужно
    }

    // MARK: - Запись статистики
    
    func recordCompletedInterval(duration: TimeInterval, projectId: UUID? = nil) {
        let now = Date()
        let interval = CompletedInterval(date: now, duration: duration, projectId: projectId)

        var intervals = getAllStoredIntervals()
        intervals.append(interval)

        saveIntervals(intervals)

        let projectInfo = projectId != nil ? " для проекта \(projectId!)" : ""
        print("📊 StatisticsManager: Записан полноценный интервал. Продолжительность: \(Int(duration/60)) мин\(projectInfo)")
    }

    func recordCompletedBreak(duration: TimeInterval, wasComputerActive: Bool, computerActiveTime: TimeInterval, qualityPercentage: Int = 100) {
        let now = Date()
        let breakRecord = CompletedBreak(
            date: now,
            duration: duration,
            wasComputerActive: wasComputerActive,
            computerActiveTime: computerActiveTime,
            qualityPercentage: qualityPercentage
        )

        var breaks = getAllStoredBreaks()
        breaks.append(breakRecord)

        saveBreaks(breaks)

        let activityInfo = wasComputerActive ? "с активностью (\(Int(computerActiveTime)) сек)" : "без активности"
        print("🌿 StatisticsManager: Записан отдых. Продолжительность: \(Int(duration/60)) мин, \(activityInfo), качество: \(qualityPercentage)%")
    }
    
    // MARK: - Получение статистики
    
    func getStatsForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getCompletedIntervalsForDateRange(from: today, to: tomorrow)
    }
    
    func getStatsForYesterday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        return getCompletedIntervalsForDateRange(from: yesterday, to: today)
    }

    // MARK: - Статистика качества отдыха

    /// Получает среднее качество отдыха за указанный период (только рабочие дни)
    func getAverageBreakQualityForPeriod(days: Int) -> Int {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: endDate)!

        let breaks = getAllStoredBreaks()
        let periodBreaks = breaks.filter { $0.date >= startDate && $0.date <= endDate }

        // Группируем отдыхи по дням
        let calendar = Calendar.current
        let groupedByDay = Dictionary(grouping: periodBreaks) { breakRecord in
            calendar.startOfDay(for: breakRecord.date)
        }

        // Считаем среднее качество только для дней когда были отдыхи (рабочие дни)
        let workingDaysQualities = groupedByDay.compactMap { (day, dayBreaks) -> Int? in
            guard !dayBreaks.isEmpty else { return nil }

            let totalQuality = dayBreaks.reduce(0) { $0 + $1.qualityPercentage }
            return totalQuality / dayBreaks.count
        }

        guard !workingDaysQualities.isEmpty else { return -1 }  // Специальное значение "нет данных"

        let averageQuality = workingDaysQualities.reduce(0, +) / workingDaysQualities.count

        print("📊 StatisticsManager: Среднее качество отдыха за \(days) дней: \(averageQuality)% (рабочих дней: \(workingDaysQualities.count))")

        return averageQuality
    }
    
    func getStatsForCurrentWeek() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        // Получаем начало недели (понедельник)
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2 // Воскресенье = 1, Понедельник = 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!
        
        return getCompletedIntervalsForDateRange(from: startOfWeek, to: endOfWeek)
    }
    
    func getStatsForCurrentMonth() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end
        
        return getCompletedIntervalsForDateRange(from: startOfMonth, to: endOfMonth)
    }
    
    func getCompletedIntervalsForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { interval in
            interval.date >= startDate && interval.date < endDate
        }.count
    }
    
    func getRecentIntervals(limit: Int = 10) -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return Array(intervals.suffix(limit).reversed()).map { ($0.date, $0.duration) }
    }

    func getAllIntervals() -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.map { ($0.date, $0.duration) }
    }
    
    func getTotalCompletedIntervals() -> Int {
        return getAllStoredIntervals().count
    }

    // MARK: - Break Statistics

    func getBreaksForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getCompletedBreaksForDateRange(from: today, to: tomorrow)
    }

    func getBreaksForCurrentWeek() -> Int {
        let calendar = Calendar.current
        let now = Date()

        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)!.start
        let endOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)!.end

        return getCompletedBreaksForDateRange(from: startOfWeek, to: endOfWeek)
    }

    func getCompletedBreaksForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let breaks = getAllStoredBreaks()
        return breaks.filter { breakRecord in
            breakRecord.date >= startDate && breakRecord.date < endDate
        }.count
    }

    func getBreakQualityForDateRange(from startDate: Date, to endDate: Date) -> (total: Int, withActivity: Int, qualityScore: Double) {
        let breaks = getAllStoredBreaks().filter { breakRecord in
            breakRecord.date >= startDate && breakRecord.date < endDate
        }

        let total = breaks.count
        let withActivity = breaks.filter { $0.wasComputerActive }.count

        // Отладочная информация
        print("🔍 StatisticsManager: getBreakQualityForDateRange")
        print("🔍 Период: \(startDate) - \(endDate)")
        print("🔍 Всего отдыхов в базе: \(getAllStoredBreaks().count)")
        print("🔍 Отдыхов в периоде: \(total)")
        if total > 0 {
            print("🔍 Детали отдыхов:")
            for (index, breakRecord) in breaks.enumerated() {
                print("🔍   \(index + 1). Дата: \(breakRecord.date), Качество: \(breakRecord.qualityPercentage)%")
            }
        }

        // Используем новое поле qualityPercentage если доступно, иначе старую логику
        let qualityScore: Double
        if total > 0 {
            let totalQuality = breaks.reduce(0) { $0 + $1.qualityPercentage }
            qualityScore = Double(totalQuality) / Double(total) / 100.0  // Конвертируем проценты в 0-1
            print("🔍 Рассчитанное качество: \(qualityScore) (из \(totalQuality) / \(total))")
        } else {
            qualityScore = -1.0  // Специальное значение "нет данных"
            print("🔍 Нет отдыхов - возвращаем -1.0")
        }

        return (total: total, withActivity: withActivity, qualityScore: qualityScore)
    }

    func getRecentBreaks(limit: Int = 10) -> [CompletedBreak] {
        let breaks = getAllStoredBreaks()
        return Array(breaks.suffix(limit).reversed())
    }

    // MARK: - Project-based Statistics

    /// Возвращает статистику по проекту за указанный период
    func getStatsForProject(_ projectId: UUID, from startDate: Date, to endDate: Date) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { interval in
            interval.projectId == projectId && interval.date >= startDate && interval.date < endDate
        }.count
    }

    /// Возвращает статистику по проекту за сегодня
    func getStatsForProjectToday(_ projectId: UUID) -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getStatsForProject(projectId, from: today, to: tomorrow)
    }

    /// Возвращает статистику по проекту за текущую неделю
    func getStatsForProjectCurrentWeek(_ projectId: UUID) -> Int {
        let calendar = Calendar.current
        let now = Date()

        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!

        return getStatsForProject(projectId, from: startOfWeek, to: endOfWeek)
    }

    /// Возвращает статистику по проекту за текущий месяц
    func getStatsForProjectCurrentMonth(_ projectId: UUID) -> Int {
        let calendar = Calendar.current
        let now = Date()

        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end

        return getStatsForProject(projectId, from: startOfMonth, to: endOfMonth)
    }

    /// Возвращает все интервалы для конкретного проекта
    func getIntervalsForProject(_ projectId: UUID) -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.filter { $0.projectId == projectId }.map { ($0.date, $0.duration) }
    }

    /// Возвращает интервалы без проекта (для миграции старых данных)
    func getIntervalsWithoutProject() -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.filter { $0.projectId == nil }.map { ($0.date, $0.duration) }
    }

    /// Возвращает статистику по всем проектам
    func getProjectStatistics() -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var projectStats: [UUID: Int] = [:]

        for interval in intervals {
            if let projectId = interval.projectId {
                projectStats[projectId, default: 0] += 1
            }
        }

        return projectStats
    }

    /// Возвращает топ проектов по количеству интервалов
    func getTopProjects(limit: Int = 5) -> [(UUID, Int)] {
        let projectStats = getProjectStatistics()
        return projectStats.sorted { $0.value > $1.value }.prefix(limit).map { ($0.key, $0.value) }
    }

    // MARK: - Work/Personal Statistics

    /// Возвращает статистику по рабочим и личным проектам
    func getWorkPersonalStatistics(projectManager: ProjectManager, from startDate: Date, to endDate: Date) -> (work: Int, personal: Int) {
        let intervals = getAllStoredIntervals().filter { interval in
            interval.date >= startDate && interval.date < endDate
        }

        var workIntervals = 0
        var personalIntervals = 0

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId) else {
                // Интервалы без проекта считаем рабочими (для обратной совместимости)
                workIntervals += 1
                continue
            }

            if project.isWorkRelated {
                workIntervals += 1
            } else {
                personalIntervals += 1
            }
        }

        return (work: workIntervals, personal: personalIntervals)
    }

    /// Возвращает статистику по рабочим и личным проектам за сегодня
    func getWorkPersonalStatisticsToday(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getWorkPersonalStatistics(projectManager: projectManager, from: today, to: tomorrow)
    }

    /// Возвращает статистику по рабочим и личным проектам за текущую неделю
    func getWorkPersonalStatisticsCurrentWeek(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let calendar = Calendar.current
        let now = Date()

        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!

        return getWorkPersonalStatistics(projectManager: projectManager, from: startOfWeek, to: endOfWeek)
    }

    /// Возвращает статистику по рабочим и личным проектам за текущий месяц
    func getWorkPersonalStatisticsCurrentMonth(projectManager: ProjectManager) -> (work: Int, personal: Int) {
        let calendar = Calendar.current
        let now = Date()

        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end

        return getWorkPersonalStatistics(projectManager: projectManager, from: startOfMonth, to: endOfMonth)
    }

    /// Возвращает детальную статистику по рабочим проектам
    func getWorkProjectsStatistics(projectManager: ProjectManager) -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var workProjectStats: [UUID: Int] = [:]

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId),
                  project.isWorkRelated else { continue }

            workProjectStats[projectId, default: 0] += 1
        }

        return workProjectStats
    }

    /// Возвращает детальную статистику по личным проектам
    func getPersonalProjectsStatistics(projectManager: ProjectManager) -> [UUID: Int] {
        let intervals = getAllStoredIntervals()
        var personalProjectStats: [UUID: Int] = [:]

        for interval in intervals {
            guard let projectId = interval.projectId,
                  let project = projectManager.getProject(by: projectId),
                  !project.isWorkRelated else { continue }

            personalProjectStats[projectId, default: 0] += 1
        }

        return personalProjectStats
    }
    
    // MARK: - Приватные методы

    func getAllStoredIntervals() -> [CompletedInterval] {
        guard let data = userDefaults.data(forKey: completedIntervalsKey) else {
            return []
        }
        
        do {
            let intervals = try JSONDecoder().decode([CompletedInterval].self, from: data)
            return intervals.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования интервалов: \(error)")
            return []
        }
    }
    
    func saveIntervals(_ intervals: [CompletedInterval]) {
        do {
            let data = try JSONEncoder().encode(intervals)
            userDefaults.set(data, forKey: completedIntervalsKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения интервалов: \(error)")
        }
    }

    // MARK: - Break Data Management

    /// Очищает все данные отдыхов (для тестирования)
    func clearAllBreaks() {
        userDefaults.removeObject(forKey: completedBreaksKey)
        print("🧹 StatisticsManager: Все данные отдыхов очищены")
    }

    /// Очищает тестовые отдыхи (короткие, менее 10 минут)
    func clearTestBreaks() {
        let breaks = getAllStoredBreaks()
        let minValidDuration: TimeInterval = 10 * 60 // 10 минут

        let validBreaks = breaks.filter { breakRecord in
            breakRecord.duration >= minValidDuration
        }

        let removedCount = breaks.count - validBreaks.count
        saveBreaks(validBreaks)

        print("🧹 StatisticsManager: Удалено \(removedCount) тестовых отдыхов (< 10 мин). Осталось: \(validBreaks.count)")
    }

    func getAllStoredBreaks() -> [CompletedBreak] {
        guard let data = userDefaults.data(forKey: completedBreaksKey) else {
            return []
        }

        do {
            let breaks = try JSONDecoder().decode([CompletedBreak].self, from: data)
            return breaks.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования отдыхов: \(error)")
            return []
        }
    }

    func saveBreaks(_ breaks: [CompletedBreak]) {
        do {
            let data = try JSONEncoder().encode(breaks)
            userDefaults.set(data, forKey: completedBreaksKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения отдыхов: \(error)")
        }
    }

    // MARK: - Data Migration

    /// Назначает проект для интервалов без проекта (миграция старых данных)
    func assignProjectToLegacyIntervals(_ projectId: UUID) {
        var intervals = getAllStoredIntervals()
        var updatedCount = 0

        for i in 0..<intervals.count {
            if intervals[i].projectId == nil {
                intervals[i] = CompletedInterval(
                    date: intervals[i].date,
                    duration: intervals[i].duration,
                    projectId: projectId
                )
                updatedCount += 1
            }
        }

        if updatedCount > 0 {
            saveIntervals(intervals)
            print("📊 StatisticsManager: Назначен проект для \(updatedCount) старых интервалов")
        }
    }

    /// Возвращает количество интервалов без проекта
    func getLegacyIntervalsCount() -> Int {
        return getAllStoredIntervals().filter { $0.projectId == nil }.count
    }

    // MARK: - Очистка данных

    func clearAllIntervals() {
        userDefaults.removeObject(forKey: completedIntervalsKey)
        print("📊 Все интервалы очищены из UserDefaults")
    }

    // MARK: - Тестовые данные

    func addTestData() {
        let calendar = Calendar.current
        let now = Date()

        // Добавляем интервалы за последние несколько дней
        for dayOffset in 1...7 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            // Случайное количество интервалов в день (2-6)
            let intervalsPerDay = Int.random(in: 2...6)

            for intervalIndex in 0..<intervalsPerDay {
                // Начинаем работу в 9 утра + случайное время
                let startHour = 9 + Int.random(in: 0...8)
                let startMinute = intervalIndex * 60 + Int.random(in: 0...30)

                if let intervalTime = calendar.date(bySettingHour: startHour, minute: startMinute, second: 0, of: workDay) {
                    // Создаем интервал с нужной датой
                    let interval = CompletedInterval(date: intervalTime, duration: 3120) // 52 минуты
                    var intervals = getAllStoredIntervals()
                    intervals.append(interval)
                    saveIntervals(intervals)
                }
            }
        }

        print("📊 Добавлены тестовые данные для демонстрации")
    }
}

// MARK: - Модель данных

struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    let projectId: UUID?        // Опциональный для обратной совместимости

    init(date: Date, duration: TimeInterval, projectId: UUID? = nil) {
        self.date = date
        self.duration = duration
        self.projectId = projectId
    }
}

struct CompletedBreak: Codable {
    let date: Date
    let duration: TimeInterval
    let wasComputerActive: Bool
    let computerActiveTime: TimeInterval
    let qualityPercentage: Int  // Процент качества отдыха (0-100)

    init(date: Date, duration: TimeInterval, wasComputerActive: Bool, computerActiveTime: TimeInterval, qualityPercentage: Int = 100) {
        self.date = date
        self.duration = duration
        self.wasComputerActive = wasComputerActive
        self.computerActiveTime = computerActiveTime
        self.qualityPercentage = qualityPercentage
    }
}
