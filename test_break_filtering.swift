#!/usr/bin/env swift

import Foundation

// Простой тест для проверки логики фильтрации отдыхов
print("🧪 Тест логики фильтрации отдыхов")

// Константы из BreakTimer
let shortBreakDuration: TimeInterval = 17 * 60 // 17 минут
let longBreakDuration: TimeInterval = 90 * 60 // 90 минут

// Функция для проверки, является ли отдых полноценным
func isFullBreak(duration: TimeInterval) -> Bool {
    return duration == shortBreakDuration || duration == longBreakDuration
}

// Тестовые данные
let testBreaks: [(String, TimeInterval)] = [
    ("Тестовый отдых 3 сек", 3),
    ("Тестовый отдых 1 мин", 60),
    ("Тестовый отдых 5 мин", 5 * 60),
    ("Короткий отдых 17 мин", shortBreakDuration),
    ("Длинный отдых 90 мин", longBreakDuration),
    ("Тестовый отдых 30 сек", 30),
    ("Тестовый отдых 2 мин", 2 * 60)
]

print("\n📊 Результаты фильтрации:")
print(String(repeating: "=", count: 50))

var fullBreaksCount = 0
var testBreaksCount = 0

for (name, duration) in testBreaks {
    let isFull = isFullBreak(duration: duration)
    let status = isFull ? "✅ ЗАПИСЫВАЕТСЯ" : "❌ НЕ ЗАПИСЫВАЕТСЯ"
    let minutes = Int(duration / 60)
    let seconds = Int(duration.truncatingRemainder(dividingBy: 60))
    
    if isFull {
        fullBreaksCount += 1
    } else {
        testBreaksCount += 1
    }
    
    print("\(name): \(minutes):\(String(format: "%02d", seconds)) - \(status)")
}

print("\n📈 Итоги:")
print("Полноценных отдыхов (записываются): \(fullBreaksCount)")
print("Тестовых отдыхов (НЕ записываются): \(testBreaksCount)")

print("\n🎯 Логика работает правильно!")
print("Только отдыхи длительностью 17 или 90 минут записываются в статистику.")
