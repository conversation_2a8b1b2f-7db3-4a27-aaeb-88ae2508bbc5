#!/usr/bin/env swift

import Cocoa

// Простой тест для проверки нового layout окна отдыха
class TestApp: NSObject, NSApplicationDelegate {
    var breakWindow: BreakStartWindow?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        print("🚀 Запуск тестового приложения для проверки layout окна отдыха")
        
        // Создаем и показываем окно отдыха
        breakWindow = BreakStartWindow()
        breakWindow?.makeKeyAndOrderFront(nil)
        breakWindow?.center()
        
        print("✅ Окно отдыха создано и показано")
        print("📏 Размер окна: \(breakWindow?.frame.size ?? CGSize.zero)")
        
        // Автоматически закрываем через 10 секунд для тестирования
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
            print("⏰ Автоматическое закрытие через 10 секунд")
            NSApplication.shared.terminate(nil)
        }
    }
}

// Запуск приложения
let app = NSApplication.shared
let delegate = TestApp()
app.delegate = delegate
app.run()
